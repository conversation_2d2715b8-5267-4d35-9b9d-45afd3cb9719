# FlowCamp UML Diagrams - Guest & Student Features

## Use Case Diagrams

### Guest User Journey
```mermaid
graph TB
    Guest[Guest User]
    
    %% Guest Use Cases
    Guest --> UC1[Browse Home Page]
    Guest --> UC2[View Program Details]
    Guest --> UC3[Explore Bootcamp Programs]
    Guest --> UC4[Browse Free Classes]
    Guest --> UC5[Read Testimonials]
    Guest --> UC6[Register Account]
    Guest --> UC7[Login to System]
    Guest --> UC8[Reset Password]
    Guest --> UC9[Verify Email]
    Guest --> UC10[Learn More About Programs]
    
    %% Home Page Features
    UC1 --> UC1a[View Hero Section]
    UC1 --> UC1b[Browse Mentor Cards]
    UC1 --> UC1c[Read Student Testimonials]
    UC1 --> UC1d[Explore Program Types]
    UC1 --> UC1e[Navigate to Other Sections]
    
    %% Program Exploration
    UC2 --> UC2a[Filter Programs by Type]
    UC2 --> UC2b[View Program Cards]
    UC2 --> UC2c[Check Mentor Information]
    UC2 --> UC2d[Compare Program Prices]
    
    %% Bootcamp Features
    UC3 --> UC3a[Browse Bootcamp Categories]
    UC3 --> UC3b[View Course Cards]
    UC3 --> UC3c[Filter by Program Type]
    UC3 --> UC3d[Access Program Details]
    
    %% Free Class Features
    UC4 --> UC4a[Filter Classes by Category]
    UC4 --> UC4b[View Class Schedule]
    UC4 --> UC4c[Check Mentor Information]
    UC4 --> UC4d[Register for Free Class]
    
    %% Authentication Flow
    UC6 --> UC6a[Fill Registration Form]
    UC6 --> UC6b[Google OAuth Registration]
    UC6 --> UC6c[Email Verification]
    
    UC7 --> UC7a[Standard Login]
    UC7 --> UC7b[Google OAuth Login]
    UC7 --> UC7c[Remember Login State]
    
    UC8 --> UC8a[Request Password Reset]
    UC8 --> UC8b[Enter Email Code]
    UC8 --> UC8c[Set New Password]
```

### Student Learning Management
```mermaid
graph TB
    Student[Student User]
    
    %% Core Learning Features
    Student --> UC11[Access Dashboard]
    Student --> UC12[Browse Available Classes]
    Student --> UC13[Study Enrolled Classes]
    Student --> UC14[Complete Assignments]
    Student --> UC15[View Certificates]
    Student --> UC16[Track Progress]
    Student --> UC17[Access Learning Materials]
    Student --> UC18[View Leaderboard]
    
    %% Dashboard Features
    UC11 --> UC11a[View Statistics Overview]
    UC11 --> UC11b[Check Recent Activity]
    UC11 --> UC11c[Monitor Class Progress]
    UC11 --> UC11d[View Assignment Status]
    UC11 --> UC11e[Access Quick Navigation]
    
    %% Class Management
    UC12 --> UC12a[Filter Classes by Category]
    UC12 --> UC12b[Search Available Classes]
    UC12 --> UC12c[View Class Details]
    UC12 --> UC12d[Enroll in Classes]
    UC12 --> UC12e[Load More Classes]
    
    UC13 --> UC13a[View Ongoing Classes]
    UC13 --> UC13b[View Completed Classes]
    UC13 --> UC13c[Access Class Materials]
    UC13 --> UC13d[Continue Learning]
    UC13 --> UC13e[Switch Between Tabs]
    
    %% Learning Materials
    UC17 --> UC17a[Browse Material List]
    UC17 --> UC17b[Read Content]
    UC17 --> UC17c[Watch Videos]
    UC17 --> UC17d[Download Files]
    UC17 --> UC17e[Mark as Complete]
    UC17 --> UC17f[Track Reading Progress]
    
    %% Assignment System
    UC14 --> UC14a[View Assignment List]
    UC14 --> UC14b[Submit Assignments]
    UC14 --> UC14c[Check Submission Status]
    UC14 --> UC14d[View Grades]
    UC14 --> UC14e[Review Feedback]
    UC14 --> UC14f[Track Task History]
    
    %% Certificate Management
    UC15 --> UC15a[View Earned Certificates]
    UC15 --> UC15b[Download Certificates]
    UC15 --> UC15c[Filter Certificates]
    UC15 --> UC15d[Search Certificates]
    UC15 --> UC15e[Sort by Date]
```

### Student Profile & Settings
```mermaid
graph TB
    Student[Student User]
    
    %% Profile Management
    Student --> UC19[Manage Profile Settings]
    Student --> UC20[Handle Notifications]
    Student --> UC21[Write Testimonials]
    Student --> UC22[Get Help Support]
    Student --> UC23[View Statistics]
    
    %% Profile Settings
    UC19 --> UC19a[Update Personal Information]
    UC19 --> UC19b[Change Profile Picture]
    UC19 --> UC19c[Update Contact Details]
    UC19 --> UC19d[Modify Bio Information]
    UC19 --> UC19e[Change Password]
    UC19 --> UC19f[Delete Account]
    UC19 --> UC19g[Sync Across Components]
    
    %% Notification Management
    UC20 --> UC20a[View Notification List]
    UC20 --> UC20b[Mark as Read]
    UC20 --> UC20c[Clear Notifications]
    UC20 --> UC20d[Filter by Status]
    UC20 --> UC20e[Persistent Storage]
    UC20 --> UC20f[Real-time Updates]
    
    %% Testimonial System
    UC21 --> UC21a[Select Class for Review]
    UC21 --> UC21b[Rate Class Experience]
    UC21 --> UC21c[Write Feedback Text]
    UC21 --> UC21d[Submit Testimonial]
    UC21 --> UC21e[Grant Permission for Display]
    
    %% Help & Support
    UC22 --> UC22a[Browse Help Categories]
    UC22 --> UC22b[Search Help Topics]
    UC22 --> UC22c[View FAQ]
    UC22 --> UC22d[Contact Support]
    UC22 --> UC22e[Access Documentation]
    
    %% Statistics & Analytics
    UC23 --> UC23a[View Learning Progress]
    UC23 --> UC23b[Check Completion Rates]
    UC23 --> UC23c[Monitor Assignment Scores]
    UC23 --> UC23d[Track Time Spent]
    UC23 --> UC23e[View Performance Charts]
```

### Student Navigation & UI Management
```mermaid
graph TB
    Student[Student User]
    
    %% Navigation Features
    Student --> UC24[Use Sidebar Navigation]
    Student --> UC25[Navigate with Navbar]
    Student --> UC26[Manage UI State]
    Student --> UC27[Handle Responsive Design]
    
    %% Sidebar Features
    UC24 --> UC24a[Toggle Sidebar Open/Close]
    UC24 --> UC24b[Search Navigation Items]
    UC24 --> UC24c[Filter Menu Options]
    UC24 --> UC24d[Quick Access Links]
    UC24 --> UC24e[Profile Information Display]
    UC24 --> UC24f[Auto-close on Navigation]
    UC24 --> UC24g[Persistent State Storage]
    
    %% Navbar Features
    UC25 --> UC25a[View Active Page Indicators]
    UC25 --> UC25b[Access Notification Dropdown]
    UC25 --> UC25c[Use Profile Dropdown]
    UC25 --> UC25d[Quick Navigation Links]
    UC25 --> UC25e[Responsive Menu Toggle]
    UC25 --> UC25f[Breadcrumb Navigation]
    
    %% UI State Management
    UC26 --> UC26a[Manage Sidebar State]
    UC26 --> UC26b[Handle Modal Dialogs]
    UC26 --> UC26c[Control Dropdown States]
    UC26 --> UC26d[Manage Loading States]
    UC26 --> UC26e[Handle Form States]
    UC26 --> UC26f[Sync UI Preferences]
    
    %% Responsive Design
    UC27 --> UC27a[Mobile Layout Adaptation]
    UC27 --> UC27b[Tablet View Optimization]
    UC27 --> UC27c[Desktop Experience]
    UC27 --> UC27d[Touch Interface Support]
    UC27 --> UC27e[Hover Interactions]
    UC27 --> UC27f[Keyboard Navigation]
```

## Activity Diagrams

### Guest to Student Authentication Workflow
```mermaid
flowchart TD
    Start([Guest Visits Site]) --> Browse{Browse as Guest?}
    
    Browse -->|Yes| GuestActions[Browse Programs/Classes/Testimonials]
    Browse -->|No| AuthChoice{Authentication Choice}
    
    GuestActions --> AuthChoice
    
    AuthChoice -->|Register| RegForm[Fill Registration Form]
    AuthChoice -->|Login| LoginForm[Fill Login Form]
    AuthChoice -->|Google OAuth| GoogleAuth[Google Authentication]
    
    RegForm --> ValidateReg{Validate Registration}
    ValidateReg -->|Invalid| RegError[Show Validation Errors]
    RegError --> RegForm
    ValidateReg -->|Valid| EmailVerify[Email Verification Process]
    
    EmailVerify --> EmailCode[Enter Verification Code]
    EmailCode --> VerifyCode{Code Valid?}
    VerifyCode -->|No| EmailCode
    VerifyCode -->|Yes| AccountCreated[Account Created Successfully]
    
    LoginForm --> ValidateLogin{Validate Credentials}
    ValidateLogin -->|Invalid| LoginError[Show Login Error]
    LoginError --> LoginForm
    ValidateLogin -->|Valid| AuthSuccess[Authentication Successful]
    
    GoogleAuth --> GoogleValidate{Google Auth Success?}
    GoogleValidate -->|No| AuthChoice
    GoogleValidate -->|Yes| AuthSuccess
    
    AccountCreated --> AuthSuccess
    AuthSuccess --> RedirectHome[Redirect to Home Page]
    RedirectHome --> StudentDashboard[Access Student Dashboard]
    StudentDashboard --> End([Student Session Active])
```

### Student Class Enrollment and Progress Workflow
```mermaid
flowchart TD
    Start([Student Accesses Classes]) --> ViewAvailable[Browse Available Classes]

    ViewAvailable --> FilterClasses{Apply Filters?}
    FilterClasses -->|Yes| ApplyFilters[Filter by Category/Search]
    FilterClasses -->|No| ClassList[View Class List]
    ApplyFilters --> ClassList

    ClassList --> SelectClass[Select Class to View]
    SelectClass --> ClassDetail[View Class Details]

    ClassDetail --> EnrollDecision{Enroll in Class?}
    EnrollDecision -->|No| ViewAvailable
    EnrollDecision -->|Yes| EnrollClass[Enroll in Class]

    EnrollClass --> UpdateStore[Update Class Store]
    UpdateStore --> SaveToStorage[Save to localStorage]
    SaveToStorage --> RedirectAcademy[Redirect to Academy]

    RedirectAcademy --> ViewEnrolled[View Enrolled Classes]
    ViewEnrolled --> TabChoice{Choose Tab}
    TabChoice -->|Studied| StudiedClasses[View Ongoing Classes]
    TabChoice -->|Completed| CompletedClasses[View Completed Classes]

    StudiedClasses --> SelectStudied[Select Class to Continue]
    SelectStudied --> AccessMaterials[Access Learning Materials]

    AccessMaterials --> MaterialType{Material Type}
    MaterialType -->|Reading| ReadMaterial[Read Content]
    MaterialType -->|Video| WatchVideo[Watch Video]
    MaterialType -->|Assignment| DoAssignment[Complete Assignment]

    ReadMaterial --> MarkRead[Mark as Read]
    WatchVideo --> MarkWatched[Mark as Watched]
    DoAssignment --> SubmitAssignment[Submit Assignment]

    MarkRead --> UpdateProgress[Update Progress]
    MarkWatched --> UpdateProgress
    SubmitAssignment --> UpdateProgress

    UpdateProgress --> CheckCompletion{Class Complete?}
    CheckCompletion -->|No| AccessMaterials
    CheckCompletion -->|Yes| GenerateCertificate[Generate Certificate]

    GenerateCertificate --> MoveToCompleted[Move to Completed Tab]
    MoveToCompleted --> End([Class Completed])
```

### Assignment Submission and Completion Process
```mermaid
flowchart TD
    Start([Student Views Assignments]) --> LoadAssignments[Load Assignment Data]

    LoadAssignments --> FilterAssignments{Apply Filters?}
    FilterAssignments -->|Yes| ApplyFilter[Filter by Status/Search]
    FilterAssignments -->|No| AssignmentList[Display Assignment List]
    ApplyFilter --> AssignmentList

    AssignmentList --> SelectAssignment[Select Assignment]
    SelectAssignment --> CheckStatus{Assignment Status}

    CheckStatus -->|Pending| StartAssignment[Start Assignment]
    CheckStatus -->|Past Due| ShowPastDue[Show Past Due Warning]
    CheckStatus -->|Submitted| ViewSubmission[View Submission Details]
    CheckStatus -->|Under Review| ShowReview[Show Under Review Status]
    CheckStatus -->|Completed| ViewGrade[View Grade and Feedback]

    ShowPastDue --> StartAssignment
    StartAssignment --> WorkOnAssignment[Work on Assignment]

    WorkOnAssignment --> SubmitDecision{Ready to Submit?}
    SubmitDecision -->|No| WorkOnAssignment
    SubmitDecision -->|Yes| SubmitAssignment[Submit Assignment]

    SubmitAssignment --> UpdateTaskStatus[Update Task Status to Submitted]
    UpdateTaskStatus --> SaveToStore[Save to Class Store]
    SaveToStore --> SaveToStorage[Save to localStorage]
    SaveToStorage --> ShowConfirmation[Show Submission Confirmation]

    ShowConfirmation --> WaitForReview[Wait for Review]
    WaitForReview --> ReviewComplete{Review Complete?}
    ReviewComplete -->|No| WaitForReview
    ReviewComplete -->|Yes| ReceiveFeedback[Receive Grade and Feedback]

    ReceiveFeedback --> UpdateFinalStatus[Update Status to Completed]
    UpdateFinalStatus --> UpdateClassProgress[Update Overall Class Progress]
    UpdateClassProgress --> CheckClassCompletion{All Materials Complete?}

    CheckClassCompletion -->|No| End([Assignment Complete])
    CheckClassCompletion -->|Yes| CompleteClass[Complete Class]
    CompleteClass --> End

    ViewSubmission --> ViewHistory[View Submission History]
    ViewGrade --> ViewHistory
    ViewHistory --> End
```

### Certificate Generation and Viewing Workflow
```mermaid
flowchart TD
    Start([Student Completes Class]) --> CheckProgress{Progress = 100%?}

    CheckProgress -->|No| ContinueLearning[Continue Learning]
    CheckProgress -->|Yes| GenerateCert[Generate Certificate]

    GenerateCert --> CreateCertData[Create Certificate Data]
    CreateCertData --> SetCertURL[Set Certificate Image URL]
    SetCertURL --> UpdateClassStatus[Update Class Status to Completed]
    UpdateClassStatus --> SaveCertificate[Save Certificate to Store]
    SaveCertificate --> ShowCertNotification[Show Certificate Available Notification]

    ShowCertNotification --> StudentChoice{Student Action}
    StudentChoice -->|View Now| ViewCertificate[Open Certificate View]
    StudentChoice -->|View Later| CertificatesPage[Go to Certificates Page]
    StudentChoice -->|Continue| Dashboard[Return to Dashboard]

    CertificatesPage --> LoadCertificates[Load All Certificates]
    LoadCertificates --> FilterCerts{Apply Filters?}
    FilterCerts -->|Yes| ApplyFilters[Filter/Search/Sort Certificates]
    FilterCerts -->|No| CertList[Display Certificate List]
    ApplyFilters --> CertList

    CertList --> SelectCert[Select Certificate to View]
    SelectCert --> ViewCertificate

    ViewCertificate --> CertActions{Certificate Action}
    CertActions -->|Download| DownloadCert[Download Certificate Image]
    CertActions -->|Close| CloseCert[Close Certificate View]

    DownloadCert --> MarkDownloaded[Mark as Downloaded]
    MarkDownloaded --> SaveDownloadStatus[Save Download Status]
    SaveDownloadStatus --> ShowDownloadSuccess[Show Download Success Message]
    ShowDownloadSuccess --> CloseCert

    CloseCert --> End([Certificate Workflow Complete])
    Dashboard --> End
    ContinueLearning --> End
```

### Student Profile Settings and Notification Management Workflow
```mermaid
flowchart TD
    Start([Student Accesses Settings]) --> LoadProfile[Load Profile Data from localStorage]

    LoadProfile --> SettingsTab{Select Settings Tab}
    SettingsTab -->|Profile| ProfileSettings[Profile Settings Tab]
    SettingsTab -->|Account| AccountSettings[Account Settings Tab]

    ProfileSettings --> ProfileActions{Profile Action}
    ProfileActions -->|Update Info| UpdateInfo[Update Personal Information]
    ProfileActions -->|Change Avatar| ChangeAvatar[Change Profile Picture]
    ProfileActions -->|Save Changes| SaveProfile[Save Profile Changes]

    ChangeAvatar --> SelectFile[Select Image File]
    SelectFile --> ValidateImage{Valid Image?}
    ValidateImage -->|No| ShowImageError[Show Image Error]
    ValidateImage -->|Yes| PreviewImage[Show Image Preview]
    ShowImageError --> SelectFile
    PreviewImage --> ConfirmAvatar{Confirm Avatar?}
    ConfirmAvatar -->|No| SelectFile
    ConfirmAvatar -->|Yes| SaveAvatar[Save Avatar to localStorage]

    UpdateInfo --> ValidateForm{Form Valid?}
    ValidateForm -->|No| ShowValidationErrors[Show Validation Errors]
    ValidateForm -->|Yes| SaveProfile
    ShowValidationErrors --> UpdateInfo

    SaveProfile --> SaveToStorage[Save to localStorage]
    SaveToStorage --> SyncComponents[Sync Across Components]
    SyncComponents --> EmitProfileUpdate[Emit Profile Update Event]
    EmitProfileUpdate --> ShowSuccessMessage[Show Success Message]

    AccountSettings --> AccountActions{Account Action}
    AccountActions -->|Change Password| ChangePassword[Change Password Form]
    AccountActions -->|Delete Account| DeleteAccount[Delete Account Process]

    ChangePassword --> ValidatePasswords{Passwords Valid?}
    ValidatePasswords -->|No| ShowPasswordErrors[Show Password Errors]
    ValidatePasswords -->|Yes| SavePassword[Save New Password]
    ShowPasswordErrors --> ChangePassword
    SavePassword --> ShowPasswordSuccess[Show Password Change Success]

    DeleteAccount --> ConfirmDelete{Confirm Deletion?}
    ConfirmDelete -->|No| AccountSettings
    ConfirmDelete -->|Yes| ClearAllData[Clear All User Data]
    ClearAllData --> LogoutUser[Logout User]
    LogoutUser --> RedirectLogin[Redirect to Login]

    ShowSuccessMessage --> End([Settings Updated])
    ShowPasswordSuccess --> End
    RedirectLogin --> End
```

## Sequence Diagrams

### Guest Authentication and Student Dashboard Redirection
```mermaid
sequenceDiagram
    participant G as Guest User
    participant LP as LoginPage
    participant R as Router
    participant LS as localStorage
    participant H as Home
    participant SD as StudentDashboard

    G->>LP: Access /login
    LP->>LP: Display login form
    G->>LP: Enter credentials
    LP->>LP: Validate form data

    alt Valid Credentials
        LP->>LP: Simulate API call
        LP->>LS: Store authentication state
        LP->>R: router.push('/')
        R->>H: Navigate to Home
        H->>H: Check authentication status
        H->>R: Redirect to student area
        R->>SD: Navigate to /student/dashboard
        SD->>SD: Load student data
        SD->>G: Display dashboard
    else Invalid Credentials
        LP->>LP: Show error message
        LP->>G: Display validation errors
    end

    Note over G,SD: Google OAuth follows similar flow
    G->>LP: Click Google Sign In
    LP->>LP: Simulate Google OAuth
    LP->>LS: Store OAuth state
    LP->>R: router.push('/')
    R->>H: Navigate to Home
    H->>R: Redirect to student dashboard
    R->>SD: Navigate to /student/dashboard
```

### Student Dashboard Data Loading and Synchronization
```mermaid
sequenceDiagram
    participant S as Student
    participant SD as StudentDashboard
    participant CS as ClassStore
    participant LS as localStorage
    participant UC as useClassStore

    S->>SD: Access /student/dashboard
    SD->>SD: Initialize component
    SD->>UC: useClassStore()
    UC->>LS: Load classes from localStorage
    UC->>CS: Initialize reactive state
    CS->>UC: Return class data
    UC->>SD: Provide computed properties

    SD->>SD: Calculate statistics
    Note over SD: completedClasses, totalLessons, etc.

    SD->>SD: Generate assignment data
    SD->>SD: Create progress charts

    loop Real-time Updates
        CS->>LS: Watch for changes
        LS->>CS: Sync data changes
        CS->>SD: Update reactive properties
        SD->>S: Re-render dashboard
    end

    S->>SD: Navigate to class
    SD->>CS: setCurrentClass(classId)
    CS->>LS: Save current class ID
    CS->>SD: Update currentClass ref
    SD->>S: Navigate to class detail
```

### Class Progress Tracking and Store Updates
```mermaid
sequenceDiagram
    participant S as Student
    participant DC as DetailClass1
    participant LM as LearningMaterials
    participant CS as ClassStore
    participant LS as localStorage

    S->>DC: Access class detail
    DC->>CS: getClassById(classId)
    CS->>DC: Return class data
    DC->>S: Display class overview

    S->>DC: Click "View Materials"
    DC->>LM: Navigate to materials
    LM->>CS: Load current class materials
    CS->>LM: Return materials array
    LM->>S: Display material list

    S->>LM: Select material
    LM->>CS: setCurrentMaterial(materialId)
    CS->>LS: Save material ID

    S->>LM: Mark material as read
    LM->>CS: markMaterialAsRead(materialId)
    CS->>CS: Update material.isRead = true
    CS->>CS: calculateProgress(classId)
    CS->>LS: Save updated class data

    alt Has Assignment
        S->>LM: Complete assignment
        LM->>CS: updateTaskStatus(materialId, 'completed')
        CS->>CS: Update material.taskStatus
        CS->>CS: Recalculate progress
        CS->>LS: Persist changes
    end

    CS->>DC: Notify progress update
    DC->>S: Update progress display

    Note over S,LS: Progress synced across all components
```

### Notification System with localStorage Persistence
```mermaid
sequenceDiagram
    participant S as Student
    participant NS as NavbarStudent
    participant LS as localStorage
    participant CS as ClassStore

    NS->>LS: Load notifications on mount
    LS->>NS: Return stored notifications
    NS->>LS: Load cleared notifications
    LS->>NS: Return cleared notification IDs

    NS->>CS: Generate class-based notifications
    CS->>NS: Return class progress/assignment data
    NS->>NS: Create notification objects
    NS->>NS: Filter out cleared notifications
    NS->>NS: Sort by read status and timestamp

    S->>NS: Click notification bell
    NS->>S: Show notification dropdown

    S->>NS: Click notification
    NS->>NS: Mark notification as read
    NS->>LS: Update notification read status
    NS->>S: Navigate to related page

    S->>NS: Clear notification
    NS->>NS: Add to cleared notifications
    NS->>LS: Save cleared notification ID
    NS->>NS: Remove from display list
    NS->>S: Update notification count

    loop Periodic Updates
        NS->>CS: Check for new class updates
        CS->>NS: Return latest class data
        NS->>NS: Generate new notifications
        NS->>LS: Persist new notifications
        NS->>S: Update notification badge
    end

    Note over S,LS: Notifications persist across page refreshes
```

### Profile Settings Updates Across Components and localStorage
```mermaid
sequenceDiagram
    participant S as Student
    participant SS as StudentSettings
    participant LS as localStorage
    participant NS as NavbarStudent
    participant SB as Sidebar
    participant W as Window

    S->>SS: Access settings page
    SS->>LS: Load user profile data
    LS->>SS: Return profile object
    SS->>S: Display current profile

    S->>SS: Update profile information
    SS->>SS: Validate form data

    alt Valid Data
        SS->>LS: Save updated profile
        LS->>SS: Confirm save success
        SS->>W: Dispatch 'userProfileUpdated' event
        W->>NS: Receive profile update event
        W->>SB: Receive profile update event

        NS->>NS: Update userName and userAvatar
        SB->>SB: Update userName and userAvatar

        SS->>S: Show success message

        Note over NS,SB: Profile synced across components

    else Invalid Data
        SS->>S: Show validation errors
    end

    S->>SS: Upload new avatar
    SS->>SS: Validate image file
    SS->>SS: Create image preview
    S->>SS: Confirm avatar change
    SS->>LS: Save avatar URL
    SS->>W: Dispatch profile update event
    W->>NS: Update avatar display
    W->>SB: Update avatar display

    Note over S,LS: Cross-tab synchronization via storage events
    LS->>W: Storage change event
    W->>SS: Handle storage change
    SS->>SS: Update profile data
```
